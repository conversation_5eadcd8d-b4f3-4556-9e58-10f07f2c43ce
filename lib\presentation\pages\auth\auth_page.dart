import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../bloc/auth/auth_bloc.dart';
import '../../widgets/auth/sign_in_form.dart';
import '../../widgets/auth/sign_up_form.dart';
import '../../widgets/common/loading_overlay.dart';
import '../../widgets/common/app_logo.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/utils/snackbar_utils.dart';

class AuthPage extends StatefulWidget {
  const AuthPage({super.key});

  @override
  State<AuthPage> createState() => _AuthPageState();
}

class _AuthPageState extends State<AuthPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthBloc, AuthState>(
      listener: (context, state) {
        setState(() {
          _isLoading = state is AuthLoading;
        });

        if (state is AuthAuthenticated) {
          context.go('/dashboard');
        } else if (state is AuthOnboardingRequired) {
          context.go('/onboarding');
        } else if (state is AuthError) {
          SnackBarUtils.showError(context, state.message);
        } else if (state is AuthPasswordResetSent) {
          SnackBarUtils.showSuccess(
            context,
            'Email de réinitialisation envoyé à ${state.email}',
          );
        }
      },
      child: LoadingOverlay(
        isLoading: _isLoading,
        child: Scaffold(
          body: SafeArea(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  const SizedBox(height: 40),
                  
                  // App Logo and Title
                  const AppLogo(size: 80),
                  const SizedBox(height: 24),
                  
                  Text(
                    'MedyTrack',
                    style: AppTextStyles.headlineLarge.copyWith(
                      color: AppColors.navy,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  
                  const SizedBox(height: 8),
                  
                  Text(
                    'Gestion professionnelle de vos médicaments',
                    style: AppTextStyles.bodyLarge.copyWith(
                      color: AppColors.grey600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  
                  const SizedBox(height: 48),
                  
                  // Tab Bar
                  Container(
                    decoration: BoxDecoration(
                      color: AppColors.grey100,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: TabBar(
                      controller: _tabController,
                      indicator: BoxDecoration(
                        color: AppColors.teal,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      labelColor: Colors.white,
                      unselectedLabelColor: AppColors.grey600,
                      labelStyle: AppTextStyles.labelLarge.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                      unselectedLabelStyle: AppTextStyles.labelLarge,
                      tabs: const [
                        Tab(text: 'Connexion'),
                        Tab(text: 'Inscription'),
                      ],
                    ),
                  ),
                  
                  const SizedBox(height: 32),
                  
                  // Tab Views
                  SizedBox(
                    height: 400,
                    child: TabBarView(
                      controller: _tabController,
                      children: [
                        SignInForm(
                          onSignIn: _handleSignIn,
                          onForgotPassword: _handleForgotPassword,
                        ),
                        SignUpForm(
                          onSignUp: _handleSignUp,
                        ),
                      ],
                    ),
                  ),
                  
                  const SizedBox(height: 32),
                  
                  // Terms and Privacy
                  Text(
                    'En continuant, vous acceptez nos Conditions d\'utilisation et notre Politique de confidentialité.',
                    style: AppTextStyles.caption.copyWith(
                      color: AppColors.grey600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _handleSignIn(String email, String password) {
    context.read<AuthBloc>().add(
          AuthSignInRequested(email: email, password: password),
        );
  }

  void _handleSignUp(String email, String password, String name) {
    context.read<AuthBloc>().add(
          AuthSignUpRequested(
            email: email,
            password: password,
            name: name,
          ),
        );
  }

  void _handleForgotPassword(String email) {
    context.read<AuthBloc>().add(
          AuthPasswordResetRequested(email),
        );
  }
}
