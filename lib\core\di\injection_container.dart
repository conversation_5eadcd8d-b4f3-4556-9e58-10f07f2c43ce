import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';
import 'package:dio/dio.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

import '../../data/datasources/auth_remote_data_source.dart';
import '../../data/datasources/medicine_remote_data_source.dart';
import '../../data/datasources/medicine_local_data_source.dart';
import '../../data/repositories/auth_repository_impl.dart';
import '../../data/repositories/medicine_repository_impl.dart';
import '../../domain/repositories/auth_repository.dart';
import '../../domain/repositories/medicine_repository.dart';
import '../../domain/usecases/auth/sign_in_usecase.dart';
import '../../domain/usecases/auth/sign_up_usecase.dart';
import '../../domain/usecases/auth/sign_out_usecase.dart';
import '../../domain/usecases/auth/get_current_user_usecase.dart';
import '../../domain/usecases/medicine/get_medicines_usecase.dart';
import '../../domain/usecases/medicine/add_medicine_usecase.dart';
import '../../domain/usecases/medicine/update_medicine_usecase.dart';
import '../../domain/usecases/medicine/delete_medicine_usecase.dart';
import '../../presentation/bloc/auth/auth_bloc.dart';
import '../../presentation/bloc/medicine/medicine_bloc.dart';
import '../../presentation/bloc/dashboard/dashboard_bloc.dart';
import '../network/network_info.dart';
import '../storage/local_storage_service.dart';
import '../storage/secure_storage_service.dart';
import '../utils/notification_service.dart';
import '../utils/camera_service.dart';
import '../utils/barcode_scanner_service.dart';

final getIt = GetIt.instance;

@InjectableInit()
Future<void> configureDependencies() async {
  // External Dependencies
  await _registerExternalDependencies();
  
  // Core Services
  _registerCoreServices();
  
  // Data Sources
  _registerDataSources();
  
  // Repositories
  _registerRepositories();
  
  // Use Cases
  _registerUseCases();
  
  // BLoCs
  _registerBlocs();
}

Future<void> _registerExternalDependencies() async {
  // Shared Preferences
  final sharedPreferences = await SharedPreferences.getInstance();
  getIt.registerLazySingleton<SharedPreferences>(() => sharedPreferences);
  
  // Secure Storage
  const secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );
  getIt.registerLazySingleton<FlutterSecureStorage>(() => secureStorage);
  
  // Supabase Client
  final supabaseClient = Supabase.instance.client;
  getIt.registerLazySingleton<SupabaseClient>(() => supabaseClient);
  
  // Dio HTTP Client
  final dio = Dio();
  dio.options.connectTimeout = const Duration(seconds: 30);
  dio.options.receiveTimeout = const Duration(seconds: 30);
  dio.options.sendTimeout = const Duration(seconds: 30);
  
  // Add interceptors
  dio.interceptors.add(LogInterceptor(
    requestBody: true,
    responseBody: true,
    logPrint: (object) {
      // Only log in debug mode
      if (const bool.fromEnvironment('DEBUG', defaultValue: false)) {
        print(object);
      }
    },
  ));
  
  getIt.registerLazySingleton<Dio>(() => dio);
  
  // Connectivity
  getIt.registerLazySingleton<Connectivity>(() => Connectivity());
}

void _registerCoreServices() {
  // Network Info
  getIt.registerLazySingleton<NetworkInfo>(
    () => NetworkInfoImpl(getIt<Connectivity>()),
  );
  
  // Local Storage Service
  getIt.registerLazySingleton<LocalStorageService>(
    () => LocalStorageServiceImpl(getIt<SharedPreferences>()),
  );
  
  // Secure Storage Service
  getIt.registerLazySingleton<SecureStorageService>(
    () => SecureStorageServiceImpl(getIt<FlutterSecureStorage>()),
  );
  
  // Notification Service
  getIt.registerLazySingleton<NotificationService>(
    () => NotificationService(),
  );
  
  // Camera Service
  getIt.registerLazySingleton<CameraService>(
    () => CameraService(),
  );
  
  // Barcode Scanner Service
  getIt.registerLazySingleton<BarcodeScannerService>(
    () => BarcodeScannerService(),
  );
}

void _registerDataSources() {
  // Auth Data Sources
  getIt.registerLazySingleton<AuthRemoteDataSource>(
    () => AuthRemoteDataSourceImpl(
      supabaseClient: getIt<SupabaseClient>(),
      secureStorage: getIt<SecureStorageService>(),
    ),
  );
  
  // Medicine Data Sources
  getIt.registerLazySingleton<MedicineRemoteDataSource>(
    () => MedicineRemoteDataSourceImpl(
      supabaseClient: getIt<SupabaseClient>(),
      dio: getIt<Dio>(),
    ),
  );
  
  getIt.registerLazySingleton<MedicineLocalDataSource>(
    () => MedicineLocalDataSourceImpl(
      localStorage: getIt<LocalStorageService>(),
    ),
  );
}

void _registerRepositories() {
  // Auth Repository
  getIt.registerLazySingleton<AuthRepository>(
    () => AuthRepositoryImpl(
      remoteDataSource: getIt<AuthRemoteDataSource>(),
      networkInfo: getIt<NetworkInfo>(),
      secureStorage: getIt<SecureStorageService>(),
    ),
  );
  
  // Medicine Repository
  getIt.registerLazySingleton<MedicineRepository>(
    () => MedicineRepositoryImpl(
      remoteDataSource: getIt<MedicineRemoteDataSource>(),
      localDataSource: getIt<MedicineLocalDataSource>(),
      networkInfo: getIt<NetworkInfo>(),
    ),
  );
}

void _registerUseCases() {
  // Auth Use Cases
  getIt.registerLazySingleton<SignInUseCase>(
    () => SignInUseCase(getIt<AuthRepository>()),
  );
  
  getIt.registerLazySingleton<SignUpUseCase>(
    () => SignUpUseCase(getIt<AuthRepository>()),
  );
  
  getIt.registerLazySingleton<SignOutUseCase>(
    () => SignOutUseCase(getIt<AuthRepository>()),
  );
  
  getIt.registerLazySingleton<GetCurrentUserUseCase>(
    () => GetCurrentUserUseCase(getIt<AuthRepository>()),
  );
  
  // Medicine Use Cases
  getIt.registerLazySingleton<GetMedicinesUseCase>(
    () => GetMedicinesUseCase(getIt<MedicineRepository>()),
  );
  
  getIt.registerLazySingleton<AddMedicineUseCase>(
    () => AddMedicineUseCase(getIt<MedicineRepository>()),
  );
  
  getIt.registerLazySingleton<UpdateMedicineUseCase>(
    () => UpdateMedicineUseCase(getIt<MedicineRepository>()),
  );
  
  getIt.registerLazySingleton<DeleteMedicineUseCase>(
    () => DeleteMedicineUseCase(getIt<MedicineRepository>()),
  );
}

void _registerBlocs() {
  // Auth BLoC
  getIt.registerFactory<AuthBloc>(
    () => AuthBloc(
      signInUseCase: getIt<SignInUseCase>(),
      signUpUseCase: getIt<SignUpUseCase>(),
      signOutUseCase: getIt<SignOutUseCase>(),
      getCurrentUserUseCase: getIt<GetCurrentUserUseCase>(),
    ),
  );
  
  // Medicine BLoC
  getIt.registerFactory<MedicineBloc>(
    () => MedicineBloc(
      getMedicinesUseCase: getIt<GetMedicinesUseCase>(),
      addMedicineUseCase: getIt<AddMedicineUseCase>(),
      updateMedicineUseCase: getIt<UpdateMedicineUseCase>(),
      deleteMedicineUseCase: getIt<DeleteMedicineUseCase>(),
    ),
  );
  
  // Dashboard BLoC
  getIt.registerFactory<DashboardBloc>(
    () => DashboardBloc(
      getMedicinesUseCase: getIt<GetMedicinesUseCase>(),
    ),
  );
}

// Helper method to reset dependencies (useful for testing)
Future<void> resetDependencies() async {
  await getIt.reset();
}

// Helper method to check if dependencies are registered
bool isDependencyRegistered<T extends Object>() {
  return getIt.isRegistered<T>();
}
