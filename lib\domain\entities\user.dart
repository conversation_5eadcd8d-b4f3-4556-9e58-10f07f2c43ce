import 'package:equatable/equatable.dart';

/// User entity representing an authenticated user
class User extends Equatable {
  final String id;
  final String email;
  final String? name;
  final String? avatarUrl;
  final String? householdId;
  final String? householdName;
  final int expiryWarningMonths;
  final bool isOnboardingCompleted;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? lastLoginAt;

  const User({
    required this.id,
    required this.email,
    this.name,
    this.avatarUrl,
    this.householdId,
    this.householdName,
    this.expiryWarningMonths = 1,
    this.isOnboardingCompleted = false,
    required this.createdAt,
    required this.updatedAt,
    this.lastLoginAt,
  });

  /// Get display name (name or email if name is not available)
  String get displayName {
    if (name != null && name!.isNotEmpty) {
      return name!;
    }
    return email.split('@').first;
  }

  /// Get initials for avatar
  String get initials {
    if (name != null && name!.isNotEmpty) {
      final parts = name!.split(' ');
      if (parts.length >= 2) {
        return '${parts[0][0]}${parts[1][0]}'.toUpperCase();
      }
      return name![0].toUpperCase();
    }
    return email[0].toUpperCase();
  }

  /// Check if user has completed onboarding
  bool get hasCompletedOnboarding {
    return isOnboardingCompleted && householdId != null;
  }

  /// Check if user is part of a household
  bool get hasHousehold {
    return householdId != null && householdId!.isNotEmpty;
  }

  /// Get greeting based on time of day
  String get timeBasedGreeting {
    final hour = DateTime.now().hour;
    if (hour < 12) {
      return 'Bonjour';
    } else if (hour < 18) {
      return 'Bon après-midi';
    } else {
      return 'Bonsoir';
    }
  }

  /// Get personalized greeting
  String get personalizedGreeting {
    return '$timeBasedGreeting, ${displayName}!';
  }

  /// Copy with method for immutable updates
  User copyWith({
    String? id,
    String? email,
    String? name,
    String? avatarUrl,
    String? householdId,
    String? householdName,
    int? expiryWarningMonths,
    bool? isOnboardingCompleted,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? lastLoginAt,
  }) {
    return User(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      householdId: householdId ?? this.householdId,
      householdName: householdName ?? this.householdName,
      expiryWarningMonths: expiryWarningMonths ?? this.expiryWarningMonths,
      isOnboardingCompleted: isOnboardingCompleted ?? this.isOnboardingCompleted,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        email,
        name,
        avatarUrl,
        householdId,
        householdName,
        expiryWarningMonths,
        isOnboardingCompleted,
        createdAt,
        updatedAt,
        lastLoginAt,
      ];

  @override
  String toString() {
    return 'User(id: $id, email: $email, name: $name, householdId: $householdId)';
  }
}

/// Household entity representing a group of users sharing medicines
class Household extends Equatable {
  final String id;
  final String name;
  final String ownerId;
  final String? description;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<User> members;

  const Household({
    required this.id,
    required this.name,
    required this.ownerId,
    this.description,
    required this.createdAt,
    required this.updatedAt,
    this.members = const [],
  });

  /// Get the number of members in the household
  int get memberCount => members.length;

  /// Check if user is the owner of the household
  bool isOwner(String userId) => ownerId == userId;

  /// Get household member by ID
  User? getMember(String userId) {
    try {
      return members.firstWhere((member) => member.id == userId);
    } catch (e) {
      return null;
    }
  }

  /// Copy with method for immutable updates
  Household copyWith({
    String? id,
    String? name,
    String? ownerId,
    String? description,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<User>? members,
  }) {
    return Household(
      id: id ?? this.id,
      name: name ?? this.name,
      ownerId: ownerId ?? this.ownerId,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      members: members ?? this.members,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        ownerId,
        description,
        createdAt,
        updatedAt,
        members,
      ];

  @override
  String toString() {
    return 'Household(id: $id, name: $name, memberCount: $memberCount)';
  }
}
