import 'package:flutter/material.dart';
import 'app_colors.dart';

/// Application text styles following Material Design 3 typography scale
/// with Ubuntu font family for consistent branding
class AppTextStyles {
  // Display Styles (Large headlines, hero text)
  static const TextStyle displayLarge = TextStyle(
    fontFamily: 'Ubuntu',
    fontSize: 57,
    fontWeight: FontWeight.w400,
    letterSpacing: -0.25,
    height: 1.12,
    color: AppColors.grey900,
  );

  static const TextStyle displayMedium = TextStyle(
    fontFamily: 'Ubuntu',
    fontSize: 45,
    fontWeight: FontWeight.w400,
    letterSpacing: 0,
    height: 1.16,
    color: AppColors.grey900,
  );

  static const TextStyle displaySmall = TextStyle(
    fontFamily: 'Ubuntu',
    fontSize: 36,
    fontWeight: FontWeight.w400,
    letterSpacing: 0,
    height: 1.22,
    color: AppColors.grey900,
  );

  // Headline Styles (Page titles, section headers)
  static const TextStyle headlineLarge = TextStyle(
    fontFamily: 'Ubuntu',
    fontSize: 32,
    fontWeight: FontWeight.w400,
    letterSpacing: 0,
    height: 1.25,
    color: AppColors.grey900,
  );

  static const TextStyle headlineMedium = TextStyle(
    fontFamily: 'Ubuntu',
    fontSize: 28,
    fontWeight: FontWeight.w400,
    letterSpacing: 0,
    height: 1.29,
    color: AppColors.grey900,
  );

  static const TextStyle headlineSmall = TextStyle(
    fontFamily: 'Ubuntu',
    fontSize: 24,
    fontWeight: FontWeight.w400,
    letterSpacing: 0,
    height: 1.33,
    color: AppColors.grey900,
  );

  // Title Styles (Card titles, dialog titles)
  static const TextStyle titleLarge = TextStyle(
    fontFamily: 'Ubuntu',
    fontSize: 22,
    fontWeight: FontWeight.w400,
    letterSpacing: 0,
    height: 1.27,
    color: AppColors.grey900,
  );

  static const TextStyle titleMedium = TextStyle(
    fontFamily: 'Ubuntu',
    fontSize: 16,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.15,
    height: 1.50,
    color: AppColors.grey900,
  );

  static const TextStyle titleSmall = TextStyle(
    fontFamily: 'Ubuntu',
    fontSize: 14,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.1,
    height: 1.43,
    color: AppColors.grey900,
  );

  // Label Styles (Button text, form labels)
  static const TextStyle labelLarge = TextStyle(
    fontFamily: 'Ubuntu',
    fontSize: 14,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.1,
    height: 1.43,
    color: AppColors.grey900,
  );

  static const TextStyle labelMedium = TextStyle(
    fontFamily: 'Ubuntu',
    fontSize: 12,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.5,
    height: 1.33,
    color: AppColors.grey900,
  );

  static const TextStyle labelSmall = TextStyle(
    fontFamily: 'Ubuntu',
    fontSize: 11,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.5,
    height: 1.45,
    color: AppColors.grey900,
  );

  // Body Styles (Main content text)
  static const TextStyle bodyLarge = TextStyle(
    fontFamily: 'Ubuntu',
    fontSize: 16,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.5,
    height: 1.50,
    color: AppColors.grey900,
  );

  static const TextStyle bodyMedium = TextStyle(
    fontFamily: 'Ubuntu',
    fontSize: 14,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.25,
    height: 1.43,
    color: AppColors.grey900,
  );

  static const TextStyle bodySmall = TextStyle(
    fontFamily: 'Ubuntu',
    fontSize: 12,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.4,
    height: 1.33,
    color: AppColors.grey900,
  );

  // Custom App-Specific Styles
  
  // Medicine Card Styles
  static const TextStyle medicineTitle = TextStyle(
    fontFamily: 'Ubuntu',
    fontSize: 18,
    fontWeight: FontWeight.w700,
    letterSpacing: 0,
    height: 1.33,
    color: AppColors.navy,
  );

  static const TextStyle medicineDosage = TextStyle(
    fontFamily: 'Ubuntu',
    fontSize: 14,
    fontWeight: FontWeight.w700,
    letterSpacing: 0.1,
    height: 1.43,
    color: AppColors.grey900,
  );

  static const TextStyle medicineDetails = TextStyle(
    fontFamily: 'Ubuntu',
    fontSize: 12,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.4,
    height: 1.33,
    color: AppColors.grey600,
  );

  // Status Badge Styles
  static const TextStyle statusBadge = TextStyle(
    fontFamily: 'Ubuntu',
    fontSize: 12,
    fontWeight: FontWeight.w700,
    letterSpacing: 0.5,
    height: 1.33,
    color: Colors.white,
  );

  // Navigation Styles
  static const TextStyle navigationLabel = TextStyle(
    fontFamily: 'Ubuntu',
    fontSize: 12,
    fontWeight: FontWeight.w600,
    letterSpacing: 0.5,
    height: 1.33,
  );

  // Form Styles
  static const TextStyle formLabel = TextStyle(
    fontFamily: 'Ubuntu',
    fontSize: 14,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.1,
    height: 1.43,
    color: AppColors.grey700,
  );

  static const TextStyle formInput = TextStyle(
    fontFamily: 'Ubuntu',
    fontSize: 16,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.5,
    height: 1.50,
    color: AppColors.grey900,
  );

  static const TextStyle formHint = TextStyle(
    fontFamily: 'Ubuntu',
    fontSize: 16,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.5,
    height: 1.50,
    color: AppColors.grey600,
  );

  static const TextStyle formError = TextStyle(
    fontFamily: 'Ubuntu',
    fontSize: 12,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.4,
    height: 1.33,
    color: AppColors.error,
  );

  // Button Styles
  static const TextStyle buttonLarge = TextStyle(
    fontFamily: 'Ubuntu',
    fontSize: 16,
    fontWeight: FontWeight.w600,
    letterSpacing: 0.1,
    height: 1.50,
  );

  static const TextStyle buttonMedium = TextStyle(
    fontFamily: 'Ubuntu',
    fontSize: 14,
    fontWeight: FontWeight.w600,
    letterSpacing: 0.1,
    height: 1.43,
  );

  static const TextStyle buttonSmall = TextStyle(
    fontFamily: 'Ubuntu',
    fontSize: 12,
    fontWeight: FontWeight.w600,
    letterSpacing: 0.5,
    height: 1.33,
  );

  // Tag Styles
  static const TextStyle tagLabel = TextStyle(
    fontFamily: 'Ubuntu',
    fontSize: 12,
    fontWeight: FontWeight.w600,
    letterSpacing: 0.5,
    height: 1.33,
    color: Colors.white,
  );

  // Statistics Styles
  static const TextStyle statisticsNumber = TextStyle(
    fontFamily: 'Ubuntu',
    fontSize: 24,
    fontWeight: FontWeight.w700,
    letterSpacing: 0,
    height: 1.33,
    color: AppColors.navy,
  );

  static const TextStyle statisticsLabel = TextStyle(
    fontFamily: 'Ubuntu',
    fontSize: 12,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.5,
    height: 1.33,
    color: AppColors.grey600,
  );

  // Caption and Helper Text
  static const TextStyle caption = TextStyle(
    fontFamily: 'Ubuntu',
    fontSize: 12,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.4,
    height: 1.33,
    color: AppColors.grey600,
  );

  static const TextStyle overline = TextStyle(
    fontFamily: 'Ubuntu',
    fontSize: 10,
    fontWeight: FontWeight.w500,
    letterSpacing: 1.5,
    height: 1.6,
    color: AppColors.grey600,
  );

  // Greeting Styles
  static const TextStyle greeting = TextStyle(
    fontFamily: 'Ubuntu',
    fontSize: 24,
    fontWeight: FontWeight.w700,
    letterSpacing: 0,
    height: 1.33,
    color: Colors.white,
  );

  static const TextStyle subGreeting = TextStyle(
    fontFamily: 'Ubuntu',
    fontSize: 16,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.5,
    height: 1.50,
    color: Colors.white,
  );

  // Helper Methods
  static TextStyle withColor(TextStyle style, Color color) {
    return style.copyWith(color: color);
  }

  static TextStyle withWeight(TextStyle style, FontWeight weight) {
    return style.copyWith(fontWeight: weight);
  }

  static TextStyle withSize(TextStyle style, double size) {
    return style.copyWith(fontSize: size);
  }

  static TextStyle withOpacity(TextStyle style, double opacity) {
    return style.copyWith(color: style.color?.withOpacity(opacity));
  }

  // Dark Theme Variants
  static TextStyle darkVariant(TextStyle style) {
    return style.copyWith(
      color: style.color == AppColors.grey900 
          ? Colors.white 
          : style.color == AppColors.grey700 
              ? AppColors.grey300 
              : style.color == AppColors.grey600 
                  ? AppColors.grey400 
                  : style.color,
    );
  }
}
