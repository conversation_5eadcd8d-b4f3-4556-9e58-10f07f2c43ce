import 'package:equatable/equatable.dart';

/// Location entity representing a storage location for medicines
class Location extends Equatable {
  final String id;
  final String name;
  final String? description;
  final String? iconName;
  final String householdId;
  final DateTime createdAt;
  final DateTime updatedAt;
  final int medicineCount;

  const Location({
    required this.id,
    required this.name,
    this.description,
    this.iconName,
    required this.householdId,
    required this.createdAt,
    required this.updatedAt,
    this.medicineCount = 0,
  });

  /// Get display name with medicine count
  String get displayNameWithCount {
    if (medicineCount > 0) {
      return '$name ($medicineCount)';
    }
    return name;
  }

  /// Get usage percentage (if total medicines is provided)
  double getUsagePercentage(int totalMedicines) {
    if (totalMedicines == 0) return 0.0;
    return (medicineCount / totalMedicines) * 100;
  }

  /// Get formatted usage percentage
  String getFormattedUsagePercentage(int totalMedicines) {
    final percentage = getUsagePercentage(totalMedicines);
    return '${percentage.toStringAsFixed(1)}%';
  }

  /// Check if location has medicines
  bool get hasMedicines => medicineCount > 0;

  /// Check if location is empty
  bool get isEmpty => medicineCount == 0;

  /// Copy with method for immutable updates
  Location copyWith({
    String? id,
    String? name,
    String? description,
    String? iconName,
    String? householdId,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? medicineCount,
  }) {
    return Location(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      iconName: iconName ?? this.iconName,
      householdId: householdId ?? this.householdId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      medicineCount: medicineCount ?? this.medicineCount,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        iconName,
        householdId,
        createdAt,
        updatedAt,
        medicineCount,
      ];

  @override
  String toString() {
    return 'Location(id: $id, name: $name, medicineCount: $medicineCount)';
  }
}

/// Predefined location types with icons
class LocationTypes {
  static const List<Map<String, String>> locations = [
    {
      'name': 'Armoire à pharmacie',
      'icon': 'medical_cabinet',
      'description': 'Armoire principale pour les médicaments',
    },
    {
      'name': 'Réfrigérateur',
      'icon': 'refrigerator',
      'description': 'Pour les médicaments nécessitant une conservation au froid',
    },
    {
      'name': 'Chambre',
      'icon': 'bedroom',
      'description': 'Médicaments personnels dans la chambre',
    },
    {
      'name': 'Cuisine',
      'icon': 'kitchen',
      'description': 'Compléments alimentaires et vitamines',
    },
    {
      'name': 'Salle de bain',
      'icon': 'bathroom',
      'description': 'Produits de soins et médicaments topiques',
    },
    {
      'name': 'Bureau',
      'icon': 'office',
      'description': 'Médicaments pour le travail',
    },
    {
      'name': 'Voiture',
      'icon': 'car',
      'description': 'Trousse de premiers secours mobile',
    },
    {
      'name': 'Sac à main',
      'icon': 'handbag',
      'description': 'Médicaments d\'urgence portables',
    },
    {
      'name': 'Trousse de voyage',
      'icon': 'travel_kit',
      'description': 'Médicaments pour les déplacements',
    },
    {
      'name': 'Autre',
      'icon': 'other',
      'description': 'Autre emplacement personnalisé',
    },
  ];

  /// Get location suggestions
  static List<String> getLocationSuggestions() {
    return locations.map((location) => location['name']!).toList();
  }

  /// Get icon for location name
  static String? getIconForLocation(String locationName) {
    try {
      final location = locations.firstWhere(
        (location) => location['name'] == locationName,
      );
      return location['icon'];
    } catch (e) {
      return 'other';
    }
  }

  /// Get description for location name
  static String? getDescriptionForLocation(String locationName) {
    try {
      final location = locations.firstWhere(
        (location) => location['name'] == locationName,
      );
      return location['description'];
    } catch (e) {
      return null;
    }
  }

  /// Check if location name is predefined
  static bool isPredefinedLocation(String locationName) {
    return locations.any((location) => location['name'] == locationName);
  }

  /// Get default location
  static String get defaultLocation => 'Armoire à pharmacie';

  /// Create Location entities from predefined data
  static List<Location> createDefaultLocations(String householdId) {
    final defaultLocations = [
      'Armoire à pharmacie',
      'Réfrigérateur',
      'Chambre',
      'Salle de bain',
    ];

    final now = DateTime.now();
    return defaultLocations.map((locationName) {
      return Location(
        id: '${householdId}_${locationName.toLowerCase().replaceAll(' ', '_')}',
        name: locationName,
        description: getDescriptionForLocation(locationName),
        iconName: getIconForLocation(locationName),
        householdId: householdId,
        createdAt: now,
        updatedAt: now,
        medicineCount: 0,
      );
    }).toList();
  }
}
