import 'package:equatable/equatable.dart';

import 'tag.dart';
import 'family_member.dart';

/// Medicine entity representing a medicine in the user's collection
class Medicine extends Equatable {
  final String id;
  final String name;
  final String? dosage;
  final String? form;
  final String? presentation;
  final int quantity;
  final DateTime expiryDate;
  final List<Tag> tags;
  final String? locationId;
  final String? locationName;
  final String? imageUrl;
  final String? notes;
  final String? barcode;
  final String? medicineId; // Reference to tunisia_medicines table
  final String? customName;
  final bool isCustom;
  final int lowStockThreshold;
  final FamilyMember? familyMember;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? laboratoire;
  final String? dci;
  final String? classe;
  final String? sousClasse;
  final String? indications;

  const Medicine({
    required this.id,
    required this.name,
    this.dosage,
    this.form,
    this.presentation,
    required this.quantity,
    required this.expiryDate,
    this.tags = const [],
    this.locationId,
    this.locationName,
    this.imageUrl,
    this.notes,
    this.barcode,
    this.medicineId,
    this.customName,
    this.isCustom = false,
    this.lowStockThreshold = 5,
    this.familyMember,
    required this.createdAt,
    required this.updatedAt,
    this.laboratoire,
    this.dci,
    this.classe,
    this.sousClasse,
    this.indications,
  });

  /// Get the complete medicine name including dosage and form
  String get completeName {
    if (isCustom && customName != null && customName!.isNotEmpty) {
      return customName!;
    }
    
    final parts = <String>[name];
    
    if (dosage != null && dosage!.isNotEmpty) {
      parts.add(dosage!);
    }
    
    if (form != null && form!.isNotEmpty) {
      parts.add(form!);
    }
    
    return parts.join(' - ');
  }

  /// Get medicine status based on expiry date and stock
  MedicineStatus get status {
    final now = DateTime.now();
    final daysUntilExpiry = expiryDate.difference(now).inDays;
    
    if (quantity == 0) {
      return MedicineStatus.outOfStock;
    }
    
    if (daysUntilExpiry < 0) {
      return MedicineStatus.expired;
    }
    
    if (quantity <= lowStockThreshold) {
      return MedicineStatus.lowStock;
    }
    
    // Check if expiring soon (within warning threshold)
    // This would typically use user's custom expiry warning threshold
    if (daysUntilExpiry <= 30) {
      return MedicineStatus.expiringSoon;
    }
    
    return MedicineStatus.adequate;
  }

  /// Get expiry status
  ExpiryStatus get expiryStatus {
    final now = DateTime.now();
    final daysUntilExpiry = expiryDate.difference(now).inDays;
    
    if (daysUntilExpiry < 0) {
      return ExpiryStatus.expired;
    }
    
    if (daysUntilExpiry <= 30) {
      return ExpiryStatus.expiringSoon;
    }
    
    return ExpiryStatus.valid;
  }

  /// Check if medicine is expired
  bool get isExpired {
    return DateTime.now().isAfter(expiryDate);
  }

  /// Check if medicine is expiring soon
  bool get isExpiringSoon {
    final now = DateTime.now();
    final daysUntilExpiry = expiryDate.difference(now).inDays;
    return daysUntilExpiry <= 30 && daysUntilExpiry >= 0;
  }

  /// Check if medicine is low stock
  bool get isLowStock {
    return quantity <= lowStockThreshold && quantity > 0;
  }

  /// Check if medicine is out of stock
  bool get isOutOfStock {
    return quantity == 0;
  }

  /// Get days until expiry
  int get daysUntilExpiry {
    return expiryDate.difference(DateTime.now()).inDays;
  }

  /// Get formatted expiry date (MM/YY format)
  String get formattedExpiryDate {
    return '${expiryDate.month.toString().padLeft(2, '0')}/${expiryDate.year.toString().substring(2)}';
  }

  /// Get therapeutic tags
  List<Tag> get therapeuticTags {
    return tags.where((tag) => tag.category == TagCategory.therapeutic).toList();
  }

  /// Get usage tags
  List<Tag> get usageTags {
    return tags.where((tag) => tag.category == TagCategory.usage).toList();
  }

  /// Copy with method for immutable updates
  Medicine copyWith({
    String? id,
    String? name,
    String? dosage,
    String? form,
    String? presentation,
    int? quantity,
    DateTime? expiryDate,
    List<Tag>? tags,
    String? locationId,
    String? locationName,
    String? imageUrl,
    String? notes,
    String? barcode,
    String? medicineId,
    String? customName,
    bool? isCustom,
    int? lowStockThreshold,
    FamilyMember? familyMember,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? laboratoire,
    String? dci,
    String? classe,
    String? sousClasse,
    String? indications,
  }) {
    return Medicine(
      id: id ?? this.id,
      name: name ?? this.name,
      dosage: dosage ?? this.dosage,
      form: form ?? this.form,
      presentation: presentation ?? this.presentation,
      quantity: quantity ?? this.quantity,
      expiryDate: expiryDate ?? this.expiryDate,
      tags: tags ?? this.tags,
      locationId: locationId ?? this.locationId,
      locationName: locationName ?? this.locationName,
      imageUrl: imageUrl ?? this.imageUrl,
      notes: notes ?? this.notes,
      barcode: barcode ?? this.barcode,
      medicineId: medicineId ?? this.medicineId,
      customName: customName ?? this.customName,
      isCustom: isCustom ?? this.isCustom,
      lowStockThreshold: lowStockThreshold ?? this.lowStockThreshold,
      familyMember: familyMember ?? this.familyMember,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      laboratoire: laboratoire ?? this.laboratoire,
      dci: dci ?? this.dci,
      classe: classe ?? this.classe,
      sousClasse: sousClasse ?? this.sousClasse,
      indications: indications ?? this.indications,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        dosage,
        form,
        presentation,
        quantity,
        expiryDate,
        tags,
        locationId,
        locationName,
        imageUrl,
        notes,
        barcode,
        medicineId,
        customName,
        isCustom,
        lowStockThreshold,
        familyMember,
        createdAt,
        updatedAt,
        laboratoire,
        dci,
        classe,
        sousClasse,
        indications,
      ];

  @override
  String toString() {
    return 'Medicine(id: $id, name: $name, quantity: $quantity, expiryDate: $expiryDate, status: $status)';
  }
}

/// Medicine status enumeration
enum MedicineStatus {
  adequate,
  lowStock,
  outOfStock,
  expired,
  expiringSoon,
}

/// Expiry status enumeration
enum ExpiryStatus {
  valid,
  expiringSoon,
  expired,
}

/// Extension methods for MedicineStatus
extension MedicineStatusExtension on MedicineStatus {
  String get displayName {
    switch (this) {
      case MedicineStatus.adequate:
        return 'Stock suffisant';
      case MedicineStatus.lowStock:
        return 'Stock faible';
      case MedicineStatus.outOfStock:
        return 'Rupture de stock';
      case MedicineStatus.expired:
        return 'Expiré';
      case MedicineStatus.expiringSoon:
        return 'Expire bientôt';
    }
  }

  String get colorCode {
    switch (this) {
      case MedicineStatus.adequate:
        return '#38A169'; // Green
      case MedicineStatus.lowStock:
        return '#D69E2E'; // Yellow
      case MedicineStatus.outOfStock:
        return '#E53E3E'; // Red
      case MedicineStatus.expired:
        return '#E53E3E'; // Red
      case MedicineStatus.expiringSoon:
        return '#ED8936'; // Orange
    }
  }
}

/// Extension methods for ExpiryStatus
extension ExpiryStatusExtension on ExpiryStatus {
  String get displayName {
    switch (this) {
      case ExpiryStatus.valid:
        return 'Valide';
      case ExpiryStatus.expiringSoon:
        return 'Expire bientôt';
      case ExpiryStatus.expired:
        return 'Expiré';
    }
  }

  String get colorCode {
    switch (this) {
      case ExpiryStatus.valid:
        return '#38A169'; // Green
      case ExpiryStatus.expiringSoon:
        return '#ED8936'; // Orange
      case ExpiryStatus.expired:
        return '#E53E3E'; // Red
    }
  }
}
