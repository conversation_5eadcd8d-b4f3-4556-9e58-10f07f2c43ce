import 'package:flutter/material.dart';

/// Application color palette following MedyTrack brand guidelines
class AppColors {
  // Primary Brand Colors
  static const Color navy = Color(0xFF2D4A8E);      // Primary navy blue
  static const Color teal = Color(0xFF0DCDB7);      // Primary teal
  
  // Navy Variations
  static const Color navyLight = Color(0xFF4A6BB8);
  static const Color navyDark = Color(0xFF1E3366);
  static const Color navyExtraLight = Color(0xFF6B8DD6);
  
  // Teal Variations
  static const Color tealLight = Color(0xFF3DDDC7);
  static const Color tealDark = Color(0xFF0AA896);
  static const Color tealExtraLight = Color(0xFF6EEDD7);
  
  // Neutral Colors
  static const Color white = Color(0xFFFFFFFF);
  static const Color black = Color(0xFF000000);
  
  // Grey Scale
  static const Color grey50 = Color(0xFFFAFAFA);
  static const Color grey100 = Color(0xFFF5F5F5);
  static const Color grey200 = Color(0xFFEEEEEE);
  static const Color grey300 = Color(0xFFE0E0E0);
  static const Color grey400 = Color(0xFFBDBDBD);
  static const Color grey500 = Color(0xFF9E9E9E);
  static const Color grey600 = Color(0xFF757575);
  static const Color grey700 = Color(0xFF616161);
  static const Color grey800 = Color(0xFF424242);
  static const Color grey900 = Color(0xFF212121);
  
  // Semantic Colors
  static const Color success = Color(0xFF4CAF50);
  static const Color successLight = Color(0xFF81C784);
  static const Color successDark = Color(0xFF388E3C);
  
  static const Color warning = Color(0xFFFF9800);
  static const Color warningLight = Color(0xFFFFB74D);
  static const Color warningDark = Color(0xFFF57C00);
  
  static const Color error = Color(0xFFF44336);
  static const Color errorLight = Color(0xFFE57373);
  static const Color errorDark = Color(0xFFD32F2F);
  
  static const Color info = Color(0xFF2196F3);
  static const Color infoLight = Color(0xFF64B5F6);
  static const Color infoDark = Color(0xFF1976D2);
  
  // Medicine Status Colors
  static const Color expired = Color(0xFFE53E3E);        // Red for expired medicines
  static const Color expiringSoon = Color(0xFFED8936);   // Orange for expiring soon
  static const Color lowStock = Color(0xFFD69E2E);       // Yellow for low stock
  static const Color adequate = Color(0xFF38A169);       // Green for adequate stock
  static const Color outOfStock = Color(0xFFE53E3E);     // Red for out of stock
  
  // Pharmaceutical Tag Colors (Therapeutic Classes)
  static const Color antibiotique = Color(0xFFE53E3E);      // Red
  static const Color antalgique = Color(0xFF3182CE);        // Blue
  static const Color antiInflammatoire = Color(0xFFD69E2E); // Yellow
  static const Color antipyretique = Color(0xFF38A169);     // Green
  static const Color antiallergique = Color(0xFF805AD5);    // Purple
  static const Color antispasmodique = Color(0xFFDD6B20);   // Orange
  static const Color corticoide = Color(0xFFC53030);        // Dark Red
  static const Color antifongique = Color(0xFF2B6CB0);      // Dark Blue
  static const Color antivirale = Color(0xFF2C7A7B);        // Teal
  static const Color antihypertenseur = Color(0xFF1A365D);  // Navy
  static const Color antidiabetique = Color(0xFF553C9A);    // Dark Purple
  static const Color psychotrope = Color(0xFF744210);       // Brown
  
  // Pharmaceutical Tag Colors (Usage Domains)
  static const Color parapharmacie = Color(0xFF0DCDB7);     // Teal
  static const Color premiersSoins = Color(0xFFE53E3E);     // Red
  static const Color complementAlimentaire = Color(0xFF38A169); // Green
  static const Color soinsPeau = Color(0xFFED8936);         // Orange
  static const Color soinsYeux = Color(0xFF3182CE);         // Blue
  static const Color soinsOreilles = Color(0xFF805AD5);     // Purple
  static const Color soinsBouche = Color(0xFFD69E2E);       // Yellow
  static const Color digestif = Color(0xFF48BB78);          // Light Green
  static const Color respiratoire = Color(0xFF4299E1);      // Light Blue
  static const Color pediatrique = Color(0xFFF56565);       // Light Red
  static const Color gynecologie = Color(0xFFED64A6);       // Pink
  static const Color dermatologie = Color(0xFFF6AD55);      // Light Orange
  
  // Background Colors
  static const Color backgroundLight = Color(0xFFFAFAFA);
  static const Color backgroundDark = Color(0xFF121212);
  static const Color surfaceLight = Color(0xFFFFFFFF);
  static const Color surfaceDark = Color(0xFF1E1E1E);
  
  // Card Colors
  static const Color cardLight = Color(0xFFFFFFFF);
  static const Color cardDark = Color(0xFF2D2D2D);
  
  // Border Colors
  static const Color borderLight = Color(0xFFE0E0E0);
  static const Color borderDark = Color(0xFF404040);
  
  // Shadow Colors
  static const Color shadowLight = Color(0x1A000000);
  static const Color shadowDark = Color(0x3A000000);
  
  // Overlay Colors
  static const Color overlayLight = Color(0x80FFFFFF);
  static const Color overlayDark = Color(0x80000000);
  
  // Gradient Colors
  static const LinearGradient navyGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [navy, navyDark],
  );
  
  static const LinearGradient tealGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [teal, tealDark],
  );
  
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [navy, teal],
  );
  
  // Helper Methods
  static Color getPharmaceuticalTagColor(String tagName) {
    switch (tagName.toLowerCase()) {
      // Therapeutic Classes
      case 'antibiotique':
        return antibiotique;
      case 'antalgique':
        return antalgique;
      case 'anti-inflammatoire':
        return antiInflammatoire;
      case 'antipyrétique':
        return antipyretique;
      case 'antiallergique':
        return antiallergique;
      case 'antispasmodique':
        return antispasmodique;
      case 'corticoïde':
        return corticoide;
      case 'antifongique':
        return antifongique;
      case 'antivirale':
        return antivirale;
      case 'antihypertenseur':
        return antihypertenseur;
      case 'antidiabétique':
        return antidiabetique;
      case 'psychotrope':
        return psychotrope;
      
      // Usage Domains
      case 'parapharmacie':
        return parapharmacie;
      case 'premiers_soins':
        return premiersSoins;
      case 'complément_alimentaire':
        return complementAlimentaire;
      case 'soins_peau':
        return soinsPeau;
      case 'soins_yeux':
        return soinsYeux;
      case 'soins_oreilles':
        return soinsOreilles;
      case 'soins_bouche':
        return soinsBouche;
      case 'digestif':
        return digestif;
      case 'respiratoire':
        return respiratoire;
      case 'pédiatrique':
        return pediatrique;
      case 'gynécologie':
        return gynecologie;
      case 'dermatologie':
        return dermatologie;
      
      default:
        return teal; // Default color
    }
  }
  
  static Color getMedicineStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'expired':
        return expired;
      case 'expiring_soon':
        return expiringSoon;
      case 'low_stock':
        return lowStock;
      case 'adequate':
        return adequate;
      case 'out_of_stock':
        return outOfStock;
      default:
        return grey500;
    }
  }
  
  static Color withOpacity(Color color, double opacity) {
    return color.withOpacity(opacity);
  }
  
  static Color lighten(Color color, [double amount = 0.1]) {
    assert(amount >= 0 && amount <= 1);
    final hsl = HSLColor.fromColor(color);
    final hslLight = hsl.withLightness((hsl.lightness + amount).clamp(0.0, 1.0));
    return hslLight.toColor();
  }
  
  static Color darken(Color color, [double amount = 0.1]) {
    assert(amount >= 0 && amount <= 1);
    final hsl = HSLColor.fromColor(color);
    final hslDark = hsl.withLightness((hsl.lightness - amount).clamp(0.0, 1.0));
    return hslDark.toColor();
  }
}
