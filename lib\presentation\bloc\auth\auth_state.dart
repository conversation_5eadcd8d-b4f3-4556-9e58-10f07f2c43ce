part of 'auth_bloc.dart';

abstract class AuthState extends Equatable {
  const AuthState();

  @override
  List<Object?> get props => [];
}

/// Initial state when the app starts
class AuthInitial extends AuthState {}

/// Loading state during authentication operations
class AuthLoading extends AuthState {}

/// State when user is authenticated and has completed onboarding
class AuthAuthenticated extends AuthState {
  final User user;

  const AuthAuthenticated(this.user);

  @override
  List<Object?> get props => [user];
}

/// State when user is authenticated but needs to complete onboarding
class AuthOnboardingRequired extends AuthState {
  final User user;

  const AuthOnboardingRequired(this.user);

  @override
  List<Object?> get props => [user];
}

/// State when user is not authenticated
class AuthUnauthenticated extends AuthState {}

/// State when an authentication error occurs
class AuthError extends AuthState {
  final String message;

  const AuthError(this.message);

  @override
  List<Object?> get props => [message];
}

/// State when password reset email is sent
class AuthPasswordResetSent extends AuthState {
  final String email;

  const AuthPasswordResetSent(this.email);

  @override
  List<Object?> get props => [email];
}

/// State when email verification is required
class AuthEmailVerificationRequired extends AuthState {
  final User user;

  const AuthEmailVerificationRequired(this.user);

  @override
  List<Object?> get props => [user];
}

/// State when email verification is sent
class AuthEmailVerificationSent extends AuthState {}

/// State when biometric authentication is available
class AuthBiometricAvailable extends AuthState {
  final User user;
  final bool isEnabled;

  const AuthBiometricAvailable(this.user, this.isEnabled);

  @override
  List<Object?> get props => [user, isEnabled];
}

/// State when biometric authentication is enabled
class AuthBiometricEnabled extends AuthState {
  final User user;

  const AuthBiometricEnabled(this.user);

  @override
  List<Object?> get props => [user];
}

/// State when biometric authentication is disabled
class AuthBiometricDisabled extends AuthState {
  final User user;

  const AuthBiometricDisabled(this.user);

  @override
  List<Object?> get props => [user];
}

/// State when account deletion is successful
class AuthAccountDeleted extends AuthState {}

/// State when session is expired
class AuthSessionExpired extends AuthState {}

/// State when account is locked due to too many failed attempts
class AuthAccountLocked extends AuthState {
  final Duration lockoutDuration;

  const AuthAccountLocked(this.lockoutDuration);

  @override
  List<Object?> get props => [lockoutDuration];
}
