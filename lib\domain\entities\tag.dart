import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

/// Tag entity representing a pharmaceutical tag for medicine categorization
class Tag extends Equatable {
  final String id;
  final String name;
  final String colorCode;
  final TagCategory category;
  final bool isSystemTag;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Tag({
    required this.id,
    required this.name,
    required this.colorCode,
    required this.category,
    this.isSystemTag = true,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Get the color from the color code
  Color get color {
    try {
      return Color(int.parse(colorCode.replaceFirst('#', '0xFF')));
    } catch (e) {
      return const Color(0xFF0DCDB7); // Default teal color
    }
  }

  /// Get the category emoji
  String get categoryEmoji {
    switch (category) {
      case TagCategory.therapeutic:
        return '💊';
      case TagCategory.usage:
        return '🩺';
    }
  }

  /// Get the category display name
  String get categoryDisplayName {
    switch (category) {
      case TagCategory.therapeutic:
        return 'Classes Thérapeutiques';
      case TagCategory.usage:
        return 'Domaines d\'Usage';
    }
  }

  /// Get the full display name with emoji
  String get displayNameWithEmoji {
    return '$categoryEmoji $name';
  }

  /// Copy with method for immutable updates
  Tag copyWith({
    String? id,
    String? name,
    String? colorCode,
    TagCategory? category,
    bool? isSystemTag,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Tag(
      id: id ?? this.id,
      name: name ?? this.name,
      colorCode: colorCode ?? this.colorCode,
      category: category ?? this.category,
      isSystemTag: isSystemTag ?? this.isSystemTag,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        colorCode,
        category,
        isSystemTag,
        createdAt,
        updatedAt,
      ];

  @override
  String toString() {
    return 'Tag(id: $id, name: $name, category: $category, isSystemTag: $isSystemTag)';
  }
}

/// Tag category enumeration
enum TagCategory {
  therapeutic, // Therapeutic classes (💊 Classes Thérapeutiques)
  usage,       // Usage domains (🩺 Domaines d'Usage)
}

/// Extension methods for TagCategory
extension TagCategoryExtension on TagCategory {
  String get name {
    switch (this) {
      case TagCategory.therapeutic:
        return 'therapeutic';
      case TagCategory.usage:
        return 'usage';
    }
  }

  String get displayName {
    switch (this) {
      case TagCategory.therapeutic:
        return 'Classes Thérapeutiques';
      case TagCategory.usage:
        return 'Domaines d\'Usage';
    }
  }

  String get emoji {
    switch (this) {
      case TagCategory.therapeutic:
        return '💊';
      case TagCategory.usage:
        return '🩺';
    }
  }

  static TagCategory fromString(String value) {
    switch (value.toLowerCase()) {
      case 'therapeutic':
        return TagCategory.therapeutic;
      case 'usage':
        return TagCategory.usage;
      default:
        return TagCategory.therapeutic;
    }
  }
}

/// Predefined pharmaceutical tags for the system
class PharmaceuticalTags {
  // Therapeutic Classes (💊 Classes Thérapeutiques)
  static const List<Map<String, dynamic>> therapeuticTags = [
    {'name': 'antibiotique', 'color': '#E53E3E'},
    {'name': 'antalgique', 'color': '#3182CE'},
    {'name': 'anti-inflammatoire', 'color': '#D69E2E'},
    {'name': 'antipyrétique', 'color': '#38A169'},
    {'name': 'antiallergique', 'color': '#805AD5'},
    {'name': 'antispasmodique', 'color': '#DD6B20'},
    {'name': 'corticoïde', 'color': '#C53030'},
    {'name': 'antifongique', 'color': '#2B6CB0'},
    {'name': 'antivirale', 'color': '#2C7A7B'},
    {'name': 'antihypertenseur', 'color': '#1A365D'},
    {'name': 'antidiabétique', 'color': '#553C9A'},
    {'name': 'psychotrope', 'color': '#744210'},
  ];

  // Usage Domains (🩺 Domaines d'Usage)
  static const List<Map<String, dynamic>> usageTags = [
    {'name': 'parapharmacie', 'color': '#0DCDB7'},
    {'name': 'premiers_soins', 'color': '#E53E3E'},
    {'name': 'complément_alimentaire', 'color': '#38A169'},
    {'name': 'soins_peau', 'color': '#ED8936'},
    {'name': 'soins_yeux', 'color': '#3182CE'},
    {'name': 'soins_oreilles', 'color': '#805AD5'},
    {'name': 'soins_bouche', 'color': '#D69E2E'},
    {'name': 'digestif', 'color': '#48BB78'},
    {'name': 'respiratoire', 'color': '#4299E1'},
    {'name': 'pédiatrique', 'color': '#F56565'},
    {'name': 'gynécologie', 'color': '#ED64A6'},
    {'name': 'dermatologie', 'color': '#F6AD55'},
  ];

  /// Get all predefined pharmaceutical tags
  static List<Map<String, dynamic>> getAllTags() {
    final allTags = <Map<String, dynamic>>[];
    
    // Add therapeutic tags
    for (final tag in therapeuticTags) {
      allTags.add({
        ...tag,
        'category': 'therapeutic',
      });
    }
    
    // Add usage tags
    for (final tag in usageTags) {
      allTags.add({
        ...tag,
        'category': 'usage',
      });
    }
    
    return allTags;
  }

  /// Get tags by category
  static List<Map<String, dynamic>> getTagsByCategory(TagCategory category) {
    switch (category) {
      case TagCategory.therapeutic:
        return therapeuticTags.map((tag) => {
          ...tag,
          'category': 'therapeutic',
        }).toList();
      case TagCategory.usage:
        return usageTags.map((tag) => {
          ...tag,
          'category': 'usage',
        }).toList();
    }
  }

  /// Get color for a specific tag name
  static String getTagColor(String tagName) {
    final allTags = getAllTags();
    final tag = allTags.firstWhere(
      (tag) => tag['name'] == tagName,
      orElse: () => {'color': '#0DCDB7'}, // Default teal color
    );
    return tag['color'] as String;
  }

  /// Check if a tag name is a predefined pharmaceutical tag
  static bool isPharmaceuticalTag(String tagName) {
    final allTags = getAllTags();
    return allTags.any((tag) => tag['name'] == tagName);
  }

  /// Get category for a specific tag name
  static TagCategory? getTagCategory(String tagName) {
    final therapeuticNames = therapeuticTags.map((tag) => tag['name']).toList();
    final usageNames = usageTags.map((tag) => tag['name']).toList();
    
    if (therapeuticNames.contains(tagName)) {
      return TagCategory.therapeutic;
    } else if (usageNames.contains(tagName)) {
      return TagCategory.usage;
    }
    
    return null;
  }

  /// Create Tag entities from predefined data
  static List<Tag> createPharmaceuticalTags(String householdId) {
    final tags = <Tag>[];
    final now = DateTime.now();
    
    for (final tagData in getAllTags()) {
      tags.add(Tag(
        id: '${householdId}_${tagData['name']}', // Generate consistent ID
        name: tagData['name'] as String,
        colorCode: tagData['color'] as String,
        category: TagCategoryExtension.fromString(tagData['category'] as String),
        isSystemTag: true,
        createdAt: now,
        updatedAt: now,
      ));
    }
    
    return tags;
  }
}
